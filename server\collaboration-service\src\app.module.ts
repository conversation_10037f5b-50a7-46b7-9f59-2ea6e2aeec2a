/**
 * 应用模块
 */
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { CollaborationModule } from './collaboration/collaboration.module';
import { MonitoringModule } from './monitoring/monitoring.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
    }),

    // JWT模块
    JwtModule.registerAsync({
      useFactory: () => ({
        secret: process.env.JWT_SECRET || 'dl-engine-secret',
        signOptions: { expiresIn: '1d' },
      }),
    }),

    // 微服务客户端模块
    ClientsModule.register([
      {
        name: 'USER_SERVICE',
        transport: Transport.TCP,
        options: {
          host: process.env.USER_SERVICE_HOST || 'localhost',
          port: parseInt(process.env.USER_SERVICE_PORT || '3002'),
        },
      },
      {
        name: 'PROJECT_SERVICE',
        transport: Transport.TCP,
        options: {
          host: process.env.PROJECT_SERVICE_HOST || 'localhost',
          port: parseInt(process.env.PROJECT_SERVICE_PORT || '3003'),
        },
      },
    ]),

    // 事件发射器模块
    EventEmitterModule.forRoot(),

    // 协作模块
    CollaborationModule,

    // 监控模块
    MonitoringModule,
  ],
})
export class AppModule {}
