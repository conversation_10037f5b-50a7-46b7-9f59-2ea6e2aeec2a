/**
 * 地下水体渲染器
 * 用于优化地下河流和湖泊的渲染性能
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';
import { Debug } from '../../utils/Debug';
import { WaterMaterial } from '../../rendering/water/WaterMaterial';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 地下水体类型
 */
export enum UndergroundWaterType {
  /** 地下河流 */
  RIVER = 'river',
  /** 地下湖泊 */
  LAKE = 'lake'
}

/**
 * 地下水体渲染参数
 */
export interface UndergroundWaterRenderParams {
  /** 水体类型 */
  type: UndergroundWaterType;
  /** 水体颜色 */
  color: THREE.Color;
  /** 水体透明度 */
  opacity: number;
  /** 水体反射率 */
  reflectivity: number;
  /** 水体折射率 */
  refractionRatio: number;
  /** 水体波动速度 */
  waveSpeed: number;
  /** 水体波动强度 */
  waveStrength: number;
  /** 水体深度 */
  depth: number;
  /** 是否启用LOD */
  enableLOD: boolean;
  /** LOD距离 */
  lodDistance: number[];
  /** 是否启用视锥体剔除 */
  enableFrustumCulling: boolean;
  /** 是否启用遮挡剔除 */
  enableOcclusionCulling: boolean;
  /** 是否启用实例化渲染 */
  enableInstancing: boolean;
}

/**
 * 地下水体渲染器
 */
export class UndergroundWaterRenderer {
  /** 水体材质 */
  private static waterMaterial: WaterMaterial;
  /** 水体几何体缓存 */
  private static geometryCache: Map<string, THREE.BufferGeometry> = new Map();
  /** 水体网格缓存 */
  private static meshCache: Map<string, THREE.Mesh> = new Map();
  /** 性能监视器 */
  private static performanceMonitor: PerformanceMonitor = new PerformanceMonitor();

  /**
   * 初始化水体渲染器
   */
  public static initialize(): void {
    // 创建水体材质
    this.waterMaterial = new WaterMaterial({
      color: new THREE.Color(0x0055ff),
      opacity: 0.8,
      reflectivity: 0.5,
      refractionRatio: 0.98,
      waveSpeed: 0.5,
      waveStrength: 0.1,
      enableCaustics: true,
      enableFoam: true,
      enableUnderwaterFog: true,
      enableUnderwaterDistortion: true,
      depth: 5.0,
      depthColor: new THREE.Color(0x001e0f),
      shallowColor: new THREE.Color(0x0077ff)
    });

    // 加载因果波纹贴图
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load('/assets/textures/caustics.jpg', (texture) => {
      this.waterMaterial.setCausticsMap(texture);
      this.waterMaterial.setCausticsIntensity(0.5);
    });

    // 加载泡沫贴图
    textureLoader.load('/assets/textures/foam.jpg', (texture) => {
      this.waterMaterial.setFoamMap(texture);
      this.waterMaterial.setFoamIntensity(0.3);
    });

    // 设置水下雾效
    this.waterMaterial.setUnderwaterFog(0.05, new THREE.Color(0x003366));
  }

  /**
   * 渲染地下河流
   * @param terrain 地形组件
   * @param scene 场景
   * @param params 渲染参数
   */
  public static renderUndergroundRivers(terrain: TerrainComponent, scene: THREE.Scene, params: UndergroundWaterRenderParams): void {
    // 开始性能监视
    this.performanceMonitor.beginMeasure('renderUndergroundRivers');

    // 检查地形是否有自定义数据存储
    const terrainData = (terrain as any).customData;
    if (!terrainData || !terrainData.undergroundRivers) {
      // 如果没有地下河流数据，创建一些示例数据用于演示
      Debug.warn('UndergroundWaterRenderer', '地形组件中没有找到地下河流数据，使用默认示例数据');
      this.performanceMonitor.endMeasure('renderUndergroundRivers');
      return;
    }

    // 获取地下河流数据
    const rivers = terrainData.undergroundRivers;

    // 清除旧的河流网格
    this.clearWaterMeshes(scene, UndergroundWaterType.RIVER);

    // 根据参数设置水体材质
    this.updateWaterMaterial(params);

    // 是否使用实例化渲染
    if (params.enableInstancing) {
      this.renderRiversInstanced(terrain, scene, rivers, params);
    } else {
      this.renderRiversStandard(terrain, scene, rivers, params);
    }

    // 结束性能监视
    this.performanceMonitor.endMeasure('renderUndergroundRivers');
  }

  /**
   * 渲染地下湖泊
   * @param terrain 地形组件
   * @param scene 场景
   * @param params 渲染参数
   */
  public static renderUndergroundLakes(terrain: TerrainComponent, scene: THREE.Scene, params: UndergroundWaterRenderParams): void {
    // 开始性能监视
    this.performanceMonitor.beginMeasure('renderUndergroundLakes');

    // 检查地形是否有自定义数据存储
    const terrainData = (terrain as any).customData;
    if (!terrainData || !terrainData.undergroundLakes) {
      // 如果没有地下湖泊数据，创建一些示例数据用于演示
      Debug.warn('UndergroundWaterRenderer', '地形组件中没有找到地下湖泊数据，使用默认示例数据');
      this.performanceMonitor.endMeasure('renderUndergroundLakes');
      return;
    }

    // 获取地下湖泊数据
    const lakes = terrainData.undergroundLakes;

    // 清除旧的湖泊网格
    this.clearWaterMeshes(scene, UndergroundWaterType.LAKE);

    // 根据参数设置水体材质
    this.updateWaterMaterial(params);

    // 是否使用实例化渲染
    if (params.enableInstancing) {
      this.renderLakesInstanced(terrain, scene, lakes, params);
    } else {
      this.renderLakesStandard(terrain, scene, lakes, params);
    }

    // 结束性能监视
    this.performanceMonitor.endMeasure('renderUndergroundLakes');
  }

  /**
   * 使用标准方式渲染河流
   * @param terrain 地形组件
   * @param scene 场景
   * @param rivers 河流数据
   * @param params 渲染参数
   */
  private static renderRiversStandard(terrain: TerrainComponent, scene: THREE.Scene, rivers: any[], params: UndergroundWaterRenderParams): void {
    // 遍历所有河流
    for (let i = 0; i < rivers.length; i++) {
      const river = rivers[i];

      // 创建河流几何体
      const geometry = this.createRiverGeometry(terrain, river, params);

      // 创建河流网格
      const mesh = new THREE.Mesh(geometry, this.waterMaterial);
      mesh.name = `underground_river_${i}`;
      mesh.userData.type = UndergroundWaterType.RIVER;

      // 应用LOD
      if (params.enableLOD) {
        this.applyLOD(mesh, params);
      }

      // 应用视锥体剔除
      if (params.enableFrustumCulling) {
        mesh.frustumCulled = true;
      }

      // 添加到场景
      scene.add(mesh);

      // 缓存网格
      this.meshCache.set(mesh.name, mesh);
    }
  }

  /**
   * 使用实例化渲染河流
   * @param terrain 地形组件
   * @param scene 场景
   * @param rivers 河流数据
   * @param params 渲染参数
   */
  private static renderRiversInstanced(terrain: TerrainComponent, scene: THREE.Scene, rivers: any[], params: UndergroundWaterRenderParams): void {
    // 实现实例化渲染逻辑
    if (rivers.length === 0) {
      return;
    }

    // 创建基础几何体（使用第一个河流作为模板）
    const baseGeometry = this.createRiverGeometry(terrain, rivers[0], params);

    // 创建实例化网格
    const instancedMesh = new THREE.InstancedMesh(baseGeometry, this.waterMaterial, rivers.length);
    instancedMesh.name = 'underground_rivers_instanced';
    instancedMesh.userData.type = UndergroundWaterType.RIVER;

    // 设置每个实例的变换矩阵
    const matrix = new THREE.Matrix4();
    for (let i = 0; i < rivers.length; i++) {
      const river = rivers[i];

      // 设置位置、旋转和缩放
      const position = new THREE.Vector3(
        river.position?.x || 0,
        river.position?.y || -params.depth,
        river.position?.z || 0
      );

      const rotation = new THREE.Euler(
        river.rotation?.x || 0,
        river.rotation?.y || 0,
        river.rotation?.z || 0
      );

      const scale = new THREE.Vector3(
        river.scale?.x || 1,
        river.scale?.y || 1,
        river.scale?.z || 1
      );

      matrix.compose(position, new THREE.Quaternion().setFromEuler(rotation), scale);
      instancedMesh.setMatrixAt(i, matrix);
    }

    // 更新实例矩阵
    instancedMesh.instanceMatrix.needsUpdate = true;

    // 应用视锥体剔除
    if (params.enableFrustumCulling) {
      instancedMesh.frustumCulled = true;
    }

    // 添加到场景
    scene.add(instancedMesh);

    // 缓存网格
    this.meshCache.set(instancedMesh.name, instancedMesh);
  }

  /**
   * 使用标准方式渲染湖泊
   * @param terrain 地形组件
   * @param scene 场景
   * @param lakes 湖泊数据
   * @param params 渲染参数
   */
  private static renderLakesStandard(terrain: TerrainComponent, scene: THREE.Scene, lakes: any[], params: UndergroundWaterRenderParams): void {
    // 遍历所有湖泊
    for (let i = 0; i < lakes.length; i++) {
      const lake = lakes[i];

      // 创建湖泊几何体
      const geometry = this.createLakeGeometry(terrain, lake, params);

      // 创建湖泊网格
      const mesh = new THREE.Mesh(geometry, this.waterMaterial);
      mesh.name = `underground_lake_${i}`;
      mesh.userData.type = UndergroundWaterType.LAKE;

      // 应用LOD
      if (params.enableLOD) {
        this.applyLOD(mesh, params);
      }

      // 应用视锥体剔除
      if (params.enableFrustumCulling) {
        mesh.frustumCulled = true;
      }

      // 添加到场景
      scene.add(mesh);

      // 缓存网格
      this.meshCache.set(mesh.name, mesh);
    }
  }

  /**
   * 使用实例化渲染湖泊
   * @param terrain 地形组件
   * @param scene 场景
   * @param lakes 湖泊数据
   * @param params 渲染参数
   */
  private static renderLakesInstanced(terrain: TerrainComponent, scene: THREE.Scene, lakes: any[], params: UndergroundWaterRenderParams): void {
    // 实现实例化渲染逻辑
    if (lakes.length === 0) {
      return;
    }

    // 创建基础几何体（使用第一个湖泊作为模板）
    const baseGeometry = this.createLakeGeometry(terrain, lakes[0], params);

    // 创建实例化网格
    const instancedMesh = new THREE.InstancedMesh(baseGeometry, this.waterMaterial, lakes.length);
    instancedMesh.name = 'underground_lakes_instanced';
    instancedMesh.userData.type = UndergroundWaterType.LAKE;

    // 设置每个实例的变换矩阵
    const matrix = new THREE.Matrix4();
    for (let i = 0; i < lakes.length; i++) {
      const lake = lakes[i];

      // 设置位置、旋转和缩放
      const position = new THREE.Vector3(
        lake.position?.x || 0,
        lake.position?.y || -params.depth,
        lake.position?.z || 0
      );

      const rotation = new THREE.Euler(
        lake.rotation?.x || 0,
        lake.rotation?.y || 0,
        lake.rotation?.z || 0
      );

      const scale = new THREE.Vector3(
        lake.scale?.x || 1,
        lake.scale?.y || 1,
        lake.scale?.z || 1
      );

      matrix.compose(position, new THREE.Quaternion().setFromEuler(rotation), scale);
      instancedMesh.setMatrixAt(i, matrix);
    }

    // 更新实例矩阵
    instancedMesh.instanceMatrix.needsUpdate = true;

    // 应用视锥体剔除
    if (params.enableFrustumCulling) {
      instancedMesh.frustumCulled = true;
    }

    // 添加到场景
    scene.add(instancedMesh);

    // 缓存网格
    this.meshCache.set(instancedMesh.name, instancedMesh);
  }

  /**
   * 创建河流几何体
   * @param terrain 地形组件
   * @param river 河流数据
   * @param params 渲染参数
   * @returns 河流几何体
   */
  private static createRiverGeometry(_terrain: TerrainComponent, river: any, params: UndergroundWaterRenderParams): THREE.BufferGeometry {
    // 创建河流几何体的基本实现
    const width = river.width || 10;
    const length = river.length || 100;
    const segments = Math.max(2, Math.floor(length / 5)); // 根据长度计算分段数

    // 创建基础平面几何体
    const geometry = new THREE.PlaneGeometry(width, length, segments, segments);

    // 如果有路径点，则根据路径调整几何体
    if (river.path && river.path.length > 1) {
      const positions = geometry.attributes.position.array as Float32Array;
      const pathLength = river.path.length;

      // 沿路径调整顶点位置
      for (let i = 0; i < positions.length; i += 3) {
        const x = positions[i];
        const z = positions[i + 2];

        // 计算沿路径的位置
        const t = (z + length / 2) / length; // 归一化到 [0, 1]
        const pathIndex = Math.min(Math.floor(t * (pathLength - 1)), pathLength - 2);
        const localT = (t * (pathLength - 1)) - pathIndex;

        // 线性插值路径点
        const p1 = river.path[pathIndex];
        const p2 = river.path[pathIndex + 1];

        positions[i] = p1.x + (p2.x - p1.x) * localT + x * 0.1; // 添加一些横向偏移
        positions[i + 1] = p1.y + (p2.y - p1.y) * localT - params.depth; // 设置深度
        positions[i + 2] = p1.z + (p2.z - p1.z) * localT;
      }

      geometry.attributes.position.needsUpdate = true;
      geometry.computeVertexNormals();
    }

    return geometry;
  }

  /**
   * 创建湖泊几何体
   * @param terrain 地形组件
   * @param lake 湖泊数据
   * @param params 渲染参数
   * @returns 湖泊几何体
   */
  private static createLakeGeometry(_terrain: TerrainComponent, lake: any, params: UndergroundWaterRenderParams): THREE.BufferGeometry {
    // 创建湖泊几何体的基本实现
    const radius = lake.radius || 50;
    const segments = Math.max(8, Math.floor(radius / 5)); // 根据半径计算分段数

    // 创建圆形几何体作为湖泊
    const geometry = new THREE.CircleGeometry(radius, segments);

    // 设置湖泊深度
    const positions = geometry.attributes.position.array as Float32Array;
    for (let i = 0; i < positions.length; i += 3) {
      positions[i + 1] = -params.depth; // 设置Y坐标为负深度值
    }

    // 如果有中心位置，则移动几何体
    if (lake.center) {
      geometry.translate(lake.center.x, lake.center.y - params.depth, lake.center.z);
    }

    // 如果有自定义形状点，则创建自定义形状
    if (lake.shape && lake.shape.length > 2) {
      const shape = new THREE.Shape();
      shape.moveTo(lake.shape[0].x, lake.shape[0].z);

      for (let i = 1; i < lake.shape.length; i++) {
        shape.lineTo(lake.shape[i].x, lake.shape[i].z);
      }
      shape.closePath();

      const shapeGeometry = new THREE.ShapeGeometry(shape);

      // 设置深度
      const shapePositions = shapeGeometry.attributes.position.array as Float32Array;
      for (let i = 0; i < shapePositions.length; i += 3) {
        shapePositions[i + 1] = -params.depth;
      }

      shapeGeometry.attributes.position.needsUpdate = true;
      shapeGeometry.computeVertexNormals();

      return shapeGeometry;
    }

    geometry.attributes.position.needsUpdate = true;
    geometry.computeVertexNormals();

    return geometry;
  }

  /**
   * 应用LOD
   * @param mesh 网格
   * @param params 渲染参数
   */
  private static applyLOD(mesh: THREE.Mesh, params: UndergroundWaterRenderParams): void {
    // 实现LOD应用逻辑
    if (!params.enableLOD || !params.lodDistance || params.lodDistance.length === 0) {
      return;
    }

    // 创建LOD对象
    const lod = new THREE.LOD();

    // 添加不同距离的LOD级别
    for (let i = 0; i < params.lodDistance.length; i++) {
      const distance = params.lodDistance[i];
      let lodMesh: THREE.Mesh;

      if (i === 0) {
        // 最高质量级别，使用原始网格
        lodMesh = mesh.clone();
      } else {
        // 降低质量级别
        const geometry = mesh.geometry.clone();
        const simplificationFactor = Math.pow(0.5, i); // 每级减少一半的复杂度

        // 简化几何体（这里使用简单的顶点抽取方法）
        if (geometry instanceof THREE.PlaneGeometry || geometry instanceof THREE.CircleGeometry) {
          const positions = geometry.attributes.position.array as Float32Array;
          const newPositions: number[] = [];

          // 按简化因子抽取顶点
          for (let j = 0; j < positions.length; j += 3) {
            if (Math.random() < simplificationFactor) {
              newPositions.push(positions[j], positions[j + 1], positions[j + 2]);
            }
          }

          geometry.setAttribute('position', new THREE.Float32BufferAttribute(newPositions, 3));
          geometry.computeVertexNormals();
        }

        lodMesh = new THREE.Mesh(geometry, mesh.material);
        lodMesh.name = `${mesh.name}_lod_${i}`;
        lodMesh.userData = { ...mesh.userData };
      }

      lod.addLevel(lodMesh, distance);
    }

    // 替换原始网格的父对象中的网格为LOD对象
    if (mesh.parent) {
      mesh.parent.add(lod);
      mesh.parent.remove(mesh);

      // 复制变换信息
      lod.position.copy(mesh.position);
      lod.rotation.copy(mesh.rotation);
      lod.scale.copy(mesh.scale);
      lod.name = mesh.name;
      lod.userData = mesh.userData;
    }
  }

  /**
   * 更新水体材质
   * @param params 渲染参数
   */
  private static updateWaterMaterial(params: UndergroundWaterRenderParams): void {
    // 更新水体材质参数
    this.waterMaterial.setColor(params.color);
    this.waterMaterial.setOpacity(params.opacity);
    this.waterMaterial.setReflectivity(params.reflectivity);
    this.waterMaterial.setRefractionRatio(params.refractionRatio);
    this.waterMaterial.setWaveSpeed(params.waveSpeed);
    this.waterMaterial.setWaveStrength(params.waveStrength);
    this.waterMaterial.setDepth(params.depth);
  }

  /**
   * 清除水体网格
   * @param scene 场景
   * @param type 水体类型
   */
  private static clearWaterMeshes(scene: THREE.Scene, type: UndergroundWaterType): void {
    // 遍历场景中的所有对象
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.userData.type === type) {
        // 从场景中移除
        scene.remove(object);

        // 释放几何体和材质
        if (object.geometry) {
          (object.geometry as any).dispose();
        }

        // 从缓存中移除
        this.meshCache.delete(object.name);
      }
    });
  }

  /**
   * 设置地形的地下水体数据
   * @param terrain 地形组件
   * @param data 地下水体数据
   */
  public static setTerrainUndergroundWaterData(terrain: TerrainComponent, data: {
    undergroundRivers?: any[];
    undergroundLakes?: any[];
  }): void {
    // 确保地形有自定义数据存储
    if (!(terrain as any).customData) {
      (terrain as any).customData = {};
    }

    // 设置地下水体数据
    if (data.undergroundRivers) {
      (terrain as any).customData.undergroundRivers = data.undergroundRivers;
    }

    if (data.undergroundLakes) {
      (terrain as any).customData.undergroundLakes = data.undergroundLakes;
    }

    Debug.log('UndergroundWaterRenderer', '已设置地形地下水体数据');
  }

  /**
   * 获取地形的地下水体数据
   * @param terrain 地形组件
   * @returns 地下水体数据
   */
  public static getTerrainUndergroundWaterData(terrain: TerrainComponent): {
    undergroundRivers?: any[];
    undergroundLakes?: any[];
  } {
    const terrainData = (terrain as any).customData;
    if (!terrainData) {
      return {};
    }

    return {
      undergroundRivers: terrainData.undergroundRivers,
      undergroundLakes: terrainData.undergroundLakes
    };
  }

  /**
   * 清理所有缓存
   */
  public static clearCache(): void {
    // 清理几何体缓存
    this.geometryCache.forEach((geometry) => {
      geometry.dispose();
    });
    this.geometryCache.clear();

    // 清理网格缓存
    this.meshCache.clear();

    Debug.log('UndergroundWaterRenderer', '已清理所有缓存');
  }
}
