"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerEntityNodes = exports.HasComponentNode = exports.RemoveComponentNode = exports.AddComponentNode = exports.GetComponentNode = exports.GetEntityNode = void 0;
/**
 * 视觉脚本实体节点
 * 提供实体操作相关的节点
 */
var ecs_1 = require("@dl-engine/ecs");
var FunctionNode_1 = require("../nodes/FunctionNode");
var Node_1 = require("../nodes/Node");
/**
 * 获取实体节点
 * 根据ID获取实体
 */
var GetEntityNode = /** @class */ (function (_super) {
    __extends(GetEntityNode, _super);
    function GetEntityNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    GetEntityNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加实体ID输入
        this.addInput({
            name: 'entityId',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'number',
            description: '实体ID',
            defaultValue: 0
        });
        // 添加实体输出
        this.addOutput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'entity',
            description: '实体'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
        // 添加失败输出流程插槽
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '失败时执行'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    GetEntityNode.prototype.execute = function () {
        // 获取输入值
        var entityId = this.getInputValue('entityId');
        // 获取实体
        var entity = entityId;
        // 检查实体是否存在
        if (entity) {
            // 设置输出值
            this.setOutputValue('entity', entity);
            // 触发输出流程
            this.triggerFlow('flow');
            return entity;
        }
        else {
            // 触发失败流程
            this.triggerFlow('fail');
            return null;
        }
    };
    return GetEntityNode;
}(FunctionNode_1.FunctionNode));
exports.GetEntityNode = GetEntityNode;
/**
 * 获取组件节点
 * 获取实体上的组件
 */
var GetComponentNode = /** @class */ (function (_super) {
    __extends(GetComponentNode, _super);
    function GetComponentNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    GetComponentNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加实体输入
        this.addInput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'entity',
            description: '实体'
        });
        // 添加组件类型输入
        this.addInput({
            name: 'componentType',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'string',
            description: '组件类型',
            defaultValue: ''
        });
        // 添加组件输出
        this.addOutput({
            name: 'component',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'component',
            description: '组件'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
        // 添加失败输出流程插槽
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '失败时执行'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    GetComponentNode.prototype.execute = function () {
        // 获取输入值
        var entity = this.getInputValue('entity');
        var componentType = this.getInputValue('componentType');
        // 检查实体和组件类型是否有效
        if (!entity || !componentType) {
            // 触发失败流程
            this.triggerFlow('fail');
            return null;
        }
        // 获取组件类
        var ComponentClass = this.getComponentClass(componentType);
        // 检查组件类是否存在
        if (!ComponentClass) {
            // 触发失败流程
            this.triggerFlow('fail');
            return null;
        }
        // 检查实体是否有该组件
        if ((0, ecs_1.hasComponent)(entity, ComponentClass)) {
            // 获取组件
            var component = (0, ecs_1.getComponent)(entity, ComponentClass);
            // 设置输出值
            this.setOutputValue('component', component);
            // 触发输出流程
            this.triggerFlow('flow');
            return component;
        }
        else {
            // 触发失败流程
            this.triggerFlow('fail');
            return null;
        }
    };
    /**
     * 获取组件类
     * @param componentType 组件类型名称
     * @returns 组件类
     */
    GetComponentNode.prototype.getComponentClass = function (componentType) {
        // 这里需要根据实际情况获取组件类
        // 可以通过全局注册表或其他方式获取
        return null;
    };
    return GetComponentNode;
}(FunctionNode_1.FunctionNode));
exports.GetComponentNode = GetComponentNode;
/**
 * 添加组件节点
 * 向实体添加组件
 */
var AddComponentNode = /** @class */ (function (_super) {
    __extends(AddComponentNode, _super);
    function AddComponentNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    AddComponentNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加实体输入
        this.addInput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'entity',
            description: '实体'
        });
        // 添加组件类型输入
        this.addInput({
            name: 'componentType',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'string',
            description: '组件类型',
            defaultValue: ''
        });
        // 添加组件数据输入
        this.addInput({
            name: 'data',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'object',
            description: '组件数据',
            defaultValue: {}
        });
        // 添加组件输出
        this.addOutput({
            name: 'component',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'component',
            description: '组件'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
        // 添加失败输出流程插槽
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '失败时执行'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    AddComponentNode.prototype.execute = function () {
        // 获取输入值
        var entity = this.getInputValue('entity');
        var componentType = this.getInputValue('componentType');
        var data = this.getInputValue('data');
        // 检查实体和组件类型是否有效
        if (!entity || !componentType) {
            // 触发失败流程
            this.triggerFlow('fail');
            return null;
        }
        // 获取组件类
        var ComponentClass = this.getComponentClass(componentType);
        // 检查组件类是否存在
        if (!ComponentClass) {
            // 触发失败流程
            this.triggerFlow('fail');
            return null;
        }
        try {
            // 添加组件
            var component = (0, ecs_1.addComponent)(entity, ComponentClass, data);
            // 设置输出值
            this.setOutputValue('component', component);
            // 触发输出流程
            this.triggerFlow('flow');
            return component;
        }
        catch (error) {
            console.error('添加组件失败:', error);
            // 触发失败流程
            this.triggerFlow('fail');
            return null;
        }
    };
    /**
     * 获取组件类
     * @param componentType 组件类型名称
     * @returns 组件类
     */
    AddComponentNode.prototype.getComponentClass = function (componentType) {
        // 这里需要根据实际情况获取组件类
        // 可以通过全局注册表或其他方式获取
        return null;
    };
    return AddComponentNode;
}(FunctionNode_1.FunctionNode));
exports.AddComponentNode = AddComponentNode;
/**
 * 移除组件节点
 * 从实体移除组件
 */
var RemoveComponentNode = /** @class */ (function (_super) {
    __extends(RemoveComponentNode, _super);
    function RemoveComponentNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    RemoveComponentNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加实体输入
        this.addInput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'entity',
            description: '实体'
        });
        // 添加组件类型输入
        this.addInput({
            name: 'componentType',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'string',
            description: '组件类型',
            defaultValue: ''
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
        // 添加失败输出流程插槽
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '失败时执行'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    RemoveComponentNode.prototype.execute = function () {
        // 获取输入值
        var entity = this.getInputValue('entity');
        var componentType = this.getInputValue('componentType');
        // 检查实体和组件类型是否有效
        if (!entity || !componentType) {
            // 触发失败流程
            this.triggerFlow('fail');
            return false;
        }
        // 获取组件类
        var ComponentClass = this.getComponentClass(componentType);
        // 检查组件类是否存在
        if (!ComponentClass) {
            // 触发失败流程
            this.triggerFlow('fail');
            return false;
        }
        try {
            // 检查实体是否有该组件
            if ((0, ecs_1.hasComponent)(entity, ComponentClass)) {
                // 移除组件
                (0, ecs_1.removeComponent)(entity, ComponentClass);
                // 触发输出流程
                this.triggerFlow('flow');
                return true;
            }
            else {
                // 触发失败流程
                this.triggerFlow('fail');
                return false;
            }
        }
        catch (error) {
            console.error('移除组件失败:', error);
            // 触发失败流程
            this.triggerFlow('fail');
            return false;
        }
    };
    /**
     * 获取组件类
     * @param componentType 组件类型名称
     * @returns 组件类
     */
    RemoveComponentNode.prototype.getComponentClass = function (componentType) {
        // 这里需要根据实际情况获取组件类
        // 可以通过全局注册表或其他方式获取
        return null;
    };
    return RemoveComponentNode;
}(FunctionNode_1.FunctionNode));
exports.RemoveComponentNode = RemoveComponentNode;
/**
 * 检查组件节点
 * 检查实体是否有指定组件
 */
var HasComponentNode = /** @class */ (function (_super) {
    __extends(HasComponentNode, _super);
    function HasComponentNode() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 初始化插槽
     */
    HasComponentNode.prototype.initializeSockets = function () {
        // 添加输入流程插槽
        this.addInput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.INPUT,
            description: '执行输入'
        });
        // 添加实体输入
        this.addInput({
            name: 'entity',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'entity',
            description: '实体'
        });
        // 添加组件类型输入
        this.addInput({
            name: 'componentType',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.INPUT,
            dataType: 'string',
            description: '组件类型',
            defaultValue: ''
        });
        // 添加结果输出
        this.addOutput({
            name: 'result',
            type: Node_1.SocketType.DATA,
            direction: Node_1.SocketDirection.OUTPUT,
            dataType: 'boolean',
            description: '检查结果'
        });
        // 添加输出流程插槽
        this.addOutput({
            name: 'flow',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '执行输出'
        });
        // 添加失败输出流程插槽
        this.addOutput({
            name: 'fail',
            type: Node_1.SocketType.FLOW,
            direction: Node_1.SocketDirection.OUTPUT,
            description: '失败时执行'
        });
    };
    /**
     * 执行节点
     * @returns 执行结果
     */
    HasComponentNode.prototype.execute = function () {
        // 获取输入值
        var entity = this.getInputValue('entity');
        var componentType = this.getInputValue('componentType');
        // 检查实体和组件类型是否有效
        if (!entity || !componentType) {
            // 触发失败流程
            this.triggerFlow('fail');
            return false;
        }
        // 获取组件类
        var ComponentClass = this.getComponentClass(componentType);
        // 检查组件类是否存在
        if (!ComponentClass) {
            // 触发失败流程
            this.triggerFlow('fail');
            return false;
        }
        // 检查实体是否有该组件
        var result = (0, ecs_1.hasComponent)(entity, ComponentClass);
        // 设置输出值
        this.setOutputValue('result', result);
        // 触发输出流程
        this.triggerFlow('flow');
        return result;
    };
    /**
     * 获取组件类
     * @param componentType 组件类型名称
     * @returns 组件类
     */
    HasComponentNode.prototype.getComponentClass = function (componentType) {
        // 这里需要根据实际情况获取组件类
        // 可以通过全局注册表或其他方式获取
        return null;
    };
    return HasComponentNode;
}(FunctionNode_1.FunctionNode));
exports.HasComponentNode = HasComponentNode;
/**
 * 注册实体节点
 * @param registry 节点注册表
 */
function registerEntityNodes(registry) {
    // 注册获取实体节点
    registry.registerNodeType({
        type: 'entity/get',
        category: Node_1.NodeCategory.ENTITY,
        constructor: GetEntityNode,
        label: '获取实体',
        description: '根据ID获取实体',
        icon: 'entity',
        color: '#4CAF50',
        tags: ['entity', 'get']
    });
    // 注册获取组件节点
    registry.registerNodeType({
        type: 'entity/component/get',
        category: Node_1.NodeCategory.ENTITY,
        constructor: GetComponentNode,
        label: '获取组件',
        description: '获取实体上的组件',
        icon: 'component',
        color: '#4CAF50',
        tags: ['entity', 'component', 'get']
    });
    // 注册添加组件节点
    registry.registerNodeType({
        type: 'entity/component/add',
        category: Node_1.NodeCategory.ENTITY,
        constructor: AddComponentNode,
        label: '添加组件',
        description: '向实体添加组件',
        icon: 'addComponent',
        color: '#4CAF50',
        tags: ['entity', 'component', 'add']
    });
    // 注册移除组件节点
    registry.registerNodeType({
        type: 'entity/component/remove',
        category: Node_1.NodeCategory.ENTITY,
        constructor: RemoveComponentNode,
        label: '移除组件',
        description: '从实体移除组件',
        icon: 'removeComponent',
        color: '#4CAF50',
        tags: ['entity', 'component', 'remove']
    });
    // 注册检查组件节点
    registry.registerNodeType({
        type: 'entity/component/has',
        category: Node_1.NodeCategory.ENTITY,
        constructor: HasComponentNode,
        label: '检查组件',
        description: '检查实体是否有指定组件',
        icon: 'hasComponent',
        color: '#4CAF50',
        tags: ['entity', 'component', 'has']
    });
}
exports.registerEntityNodes = registerEntityNodes;
