import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'IREngine',
      fileName: 'dl-engine',
    },
    rollupOptions: {
      external: ['three', 'cannon-es'],
      output: {
        globals: {
          three: 'THREE',
          'cannon-es': 'CANNON',
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
});
