/**
 * 事件总线服务
 */
import { Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { Redis } from 'ioredis';
import { InjectRedis } from '@nestjs/redis';
import { v4 as uuidv4 } from 'uuid';
import { EVENT_BUS_OPTIONS } from './event-bus.constants';
import { Event, EventBusOptions } from './event-bus.interface';

@Injectable()
export class EventBusService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EventBusService.name);
  private readonly localHandlers = new Map<string, Set<Function>>();
  private readonly channelPrefix: string;
  private readonly serviceName: string;
  private subscriber: Redis;
  private publisher: Redis;
  private isInitialized = false;

  constructor(
    @Inject(EVENT_BUS_OPTIONS) private readonly options: EventBusOptions,
    @InjectRedis() private readonly redis: Redis,
  ) {
    this.channelPrefix = options.channelPrefix || 'dl-engine:events:';
    this.serviceName = options.serviceName;
  }

  /**
   * 模块初始化
   */
  async onModuleInit() {
    if (this.options.redis) {
      // 创建订阅者和发布者客户端
      this.subscriber = this.redis;
      this.publisher = this.redis.duplicate();
      
      // 订阅所有事件通道
      await this.subscriber.subscribe(`${this.channelPrefix}*`);
      
      // 处理接收到的消息
      this.subscriber.on('message', (channel, message) => {
        try {
          const event = JSON.parse(message) as Event;
          const eventName = channel.replace(this.channelPrefix, '');
          
          // 不处理自己发布的事件
          if (event.publisher === this.serviceName) {
            return;
          }
          
          this.logger.debug(`收到事件: ${eventName}, 发布者: ${event.publisher}`);
          this.processLocalHandlers(eventName, event);
        } catch (error) {
          this.logger.error(`处理事件消息失败: ${error.message}`, error.stack);
        }
      });
      
      this.logger.log(`事件总线已初始化，服务名称: ${this.serviceName}`);
      this.isInitialized = true;
    } else if (this.options.enableLocalEvents) {
      this.logger.log(`事件总线已初始化（仅本地模式），服务名称: ${this.serviceName}`);
      this.isInitialized = true;
    } else {
      this.logger.warn('事件总线未初始化，未配置Redis且未启用本地事件');
    }
  }

  /**
   * 模块销毁
   */
  async onModuleDestroy() {
    if (this.subscriber) {
      await this.subscriber.unsubscribe();
      await this.subscriber.quit();
    }
    
    if (this.publisher) {
      await this.publisher.quit();
    }
    
    this.logger.log('事件总线已关闭');
  }

  /**
   * 发布事件
   * @param eventName 事件名称
   * @param data 事件数据
   * @param options 事件选项
   */
  async publish<T = any>(
    eventName: string,
    data: T,
    options: {
      correlationId?: string;
      causationId?: string;
    } = {},
  ): Promise<void> {
    const event: Event = {
      name: eventName,
      data,
      timestamp: Date.now(),
      publisher: this.serviceName,
      id: uuidv4(),
      version: 1,
      correlationId: options.correlationId,
      causationId: options.causationId,
    };

    // 处理本地事件
    if (this.options.enableLocalEvents !== false) {
      this.processLocalHandlers(eventName, event);
    }

    // 发布到Redis
    if (this.publisher) {
      try {
        const channel = `${this.channelPrefix}${eventName}`;
        await this.publisher.publish(channel, JSON.stringify(event));
        this.logger.debug(`已发布事件: ${eventName}`);
      } catch (error) {
        this.logger.error(`发布事件失败: ${error.message}`, error.stack);
        throw error;
      }
    }
  }

  /**
   * 订阅事件
   * @param eventName 事件名称
   * @param handler 事件处理函数
   */
  subscribe<T extends Event = Event>(eventName: string, handler: (event: T) => Promise<void>): () => void {
    if (!this.localHandlers.has(eventName)) {
      this.localHandlers.set(eventName, new Set());
    }
    
    this.localHandlers.get(eventName).add(handler);
    this.logger.debug(`已订阅事件: ${eventName}`);
    
    // 返回取消订阅函数
    return () => {
      if (this.localHandlers.has(eventName)) {
        this.localHandlers.get(eventName).delete(handler);
        if (this.localHandlers.get(eventName).size === 0) {
          this.localHandlers.delete(eventName);
        }
      }
    };
  }

  /**
   * 处理本地事件处理器
   * @param eventName 事件名称
   * @param event 事件对象
   */
  private async processLocalHandlers(eventName: string, event: Event): Promise<void> {
    const handlers = this.localHandlers.get(eventName);
    if (!handlers || handlers.size === 0) {
      return;
    }
    
    for (const handler of handlers) {
      try {
        await handler(event);
      } catch (error) {
        this.logger.error(`事件处理器执行失败: ${error.message}`, error.stack);
      }
    }
  }
}
